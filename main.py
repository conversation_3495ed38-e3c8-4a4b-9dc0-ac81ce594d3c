#!/usr/bin/env python3
"""
Main entry point for the Spain Stores project.
"""

def _get_api_summary(self, text):
        """Use AI to summarize the reported failure text"""
                   
        try:
            
            if not secret.ai_api_key:
                return "API key not configured"
                
            import requests
            import json
            
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {secret.ai_api_key}",
                "HTTP-Referer": self.env['ir.config_parameter'].sudo().get_param('web.base.url', False),
                "X-Title": "Odoo RMA Tracker"
            }
            system_prompt = """
                            Extract and Quote Fault Descriptions from data provided in an RMA submission.
                            Group and sum the information.
                            Focus on:
                            Product identifiers/models/Fault Description and count
                            
                            
                            Format your response as a brief, factual summary without asking questions.
                            If information is missing, simply summarize what is provided. Dont say that something hasnt been provided it may have been in another way.
                            Start the response with "The customer has reported the following:"
                            """
            
            payload = {
                        "model": "nousresearch/deephermes-3-mistral-24b-preview:free",
                        "messages": [
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": text}
                        ],
                        "max_tokens": 150,
                        "temperature": 0.3  # Lower temperature for more factual responses
                        }
            
            response = requests.post("https://openrouter.ai/api/v1/chat/completions", 
                                    headers=headers, 
                                    data=json.dumps(payload))
            
            if response.status_code == 200:
                return response.json()["choices"][0]["message"]["content"].strip()
            else:
                return f"API Error: {response.status_code} - {response.text}"
        except Exception as e:
            return f"Error: {str(e)}"

def main():
    """Main function to run the application."""
    print("Hello, Spain Stores!")
    print("Welcome to your new Python project!")
    
    # Add your application logic here
    pass

if __name__ == "__main__":
    main()
